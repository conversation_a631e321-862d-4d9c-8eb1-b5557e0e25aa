"""
脉冲星分类Baseline模型训练脚本

基于物理约束的Res2Net架构，实现94-97%准确率的baseline性能
集成混合通道适配器、SE注意力机制和物理约束数据增强

使用方法：
    python train_baseline.py --config ../configs/baseline.yaml
    python train_baseline.py --config ../configs/baseline.yaml --resume checkpoints/latest_checkpoint.pth
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader

# 导入自定义模块
from configs.base_config import load_config
from models.factory import create_pulsar_res2net, get_model_info
from data.htru_dataset import create_htru_datasets, create_htru_dataloaders
from data.data_loader import create_efficient_dataloader, analyze_dataloader
from training.trainer import PulsarTrainer
from utils.logger import ExperimentLogger
from utils.reproducibility import ReproducibilityManager


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train Pulsar Classification Baseline Model')

    parser.add_argument(
        '--config',
        type=str,
        required=True,
        help='Path to configuration file'
    )

    parser.add_argument(
        '--resume',
        type=str,
        default=None,
        help='Path to checkpoint to resume from'
    )

    parser.add_argument(
        '--device',
        type=str,
        default=None,
        help='Device to use (cuda/cpu). If not specified, will use config setting'
    )

    parser.add_argument(
        '--data-root',
        type=str,
        default=None,
        help='Override data root directory'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=None,
        help='Override batch size'
    )

    parser.add_argument(
        '--epochs',
        type=int,
        default=None,
        help='Override number of epochs'
    )

    parser.add_argument(
        '--lr',
        type=float,
        default=None,
        help='Override learning rate'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode (reduced dataset size)'
    )

    return parser.parse_args()


def override_config(config, args):
    """根据命令行参数覆盖配置"""
    if args.device:
        config.hardware.device = args.device

    if args.data_root:
        config.data.data_root = args.data_root

    if args.batch_size:
        config.data.batch_size = args.batch_size

    if args.epochs:
        config.training.epochs = args.epochs

    if args.lr:
        config.training.optimizer['lr'] = args.lr

    if args.debug:
        # 调试模式：减少数据量和训练轮数
        config.training.epochs = 5
        config.data.batch_size = 8
        config.validation.early_stopping_patience = 3
        config.logging.log_interval = 1


def setup_device(config):
    """设置训练设备"""
    device_name = config.hardware.device

    if device_name == "cuda" and torch.cuda.is_available():
        device = torch.device("cuda")
        print(f"Using GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        device = torch.device("cpu")
        print("Using CPU")

    return device


def create_model(config, device):
    """创建模型"""
    print("Creating model...")

    # 转换配置为字典格式
    model_config = {
        'layers': config.model.layers,
        'num_classes': config.model.num_classes,
        'width': config.model.width,
        'scales': config.model.scales,
        'attention_type': config.model.attention_type,
        'dropout_rate': config.model.dropout_rate,
        'zero_init_residual': config.model.zero_init_residual,
        'groups': config.model.groups,
        'channel_adapter': config.model.channel_adapter
    }

    model = create_pulsar_res2net(model_config)
    model = model.to(device)

    # 获取模型信息
    model_info = get_model_info(model)
    print(f"Model created: {model_info['total_parameters']:,} parameters")
    print(f"Model size: {model_info['model_size_mb']:.2f} MB")

    return model, model_info


def create_datasets_and_loaders(config):
    """创建数据集和数据加载器"""
    print("Creating datasets...")

    # 创建数据集
    datasets = create_htru_datasets(
        data_root=config.data.data_root,
        modality=config.data.modality,
        transform_config=config.data.augmentation.get('train_config', 'moderate'),
        val_split=config.data.val_split,
        test_split=config.data.test_split,
        random_state=config.data.random_state
    )

    train_dataset, val_dataset, test_dataset = datasets

    print(f"Dataset sizes:")
    print(f"  Train: {len(train_dataset)}")
    print(f"  Validation: {len(val_dataset)}")
    print(f"  Test: {len(test_dataset)}")

    # 打印类别分布
    train_dist = train_dataset.get_class_distribution()
    print(f"Training set class distribution: {train_dist}")

    # 创建数据加载器
    dataloaders = create_htru_dataloaders(
        datasets,
        batch_size=config.data.batch_size,
        num_workers=config.data.num_workers,
        pin_memory=config.data.pin_memory
    )

    train_loader, val_loader, test_loader = dataloaders

    # 分析数据加载器（可选）
    if hasattr(config.data, 'analyze_loader') and getattr(config.data, 'analyze_loader', False):
        print("Analyzing data loaders...")
        train_stats = analyze_dataloader(train_loader)
        print(f"Training loader stats: {train_stats}")

    return train_loader, val_loader, test_loader


def load_checkpoint(model, optimizer, scheduler, checkpoint_path, device):
    """加载检查点"""
    print(f"Loading checkpoint from {checkpoint_path}")

    checkpoint = torch.load(checkpoint_path, map_location=device)

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 加载优化器状态
    if optimizer and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    # 加载调度器状态
    if scheduler and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

    start_epoch = checkpoint.get('epoch', 0) + 1
    best_metrics = checkpoint.get('metrics', {})

    print(f"Resumed from epoch {start_epoch}")
    print(f"Previous best metrics: {best_metrics}")

    return start_epoch, best_metrics


def evaluate_test_set(model, test_loader, device, logger, save_dir, experiment_logger):
    """评估测试集性能并生成详细分析"""
    from training.metrics import MetricsCalculator
    import json
    import torch.nn.functional as F

    # 加载最佳检查点
    best_checkpoint_path = os.path.join(save_dir, 'best_checkpoint.pth')
    if os.path.exists(best_checkpoint_path):
        checkpoint = torch.load(best_checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Loaded best checkpoint from epoch {checkpoint['epoch']}")
    else:
        logger.warning("Best checkpoint not found, using current model weights")

    model.eval()
    metrics_calculator = MetricsCalculator(num_classes=2)
    misclassified_samples = []

    with torch.no_grad():
        for batch_idx, (inputs, targets) in enumerate(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)

            # 计算预测概率和预测类别
            probs = F.softmax(outputs, dim=1)
            preds = torch.argmax(outputs, dim=1)

            # 收集误分类样本信息
            for i in range(len(targets)):
                if preds[i] != targets[i]:
                    # 获取样本信息（这里简化处理，实际应该从dataset获取路径）
                    sample_info = {
                        'path': f'batch_{batch_idx}_sample_{i}',  # 简化的路径信息
                        'true_label': targets[i].item(),
                        'pred_label': preds[i].item(),
                        'confidence': probs[i][preds[i]].item()
                    }
                    misclassified_samples.append(sample_info)

            metrics_calculator.update(outputs, targets)

    # 计算测试指标
    test_metrics = metrics_calculator.compute()
    confusion_matrix = metrics_calculator.get_confusion_matrix()

    # 保存混淆矩阵可视化（仅在测试集评估后生成）
    class_names = ['Non-Pulsar', 'Pulsar']
    experiment_logger.save_confusion_matrix(confusion_matrix, class_names, 'final_test')

    # 保存误分类样本分析
    experiment_logger.save_misclassified_analysis(misclassified_samples)

    # 保存测试结果
    test_results = {
        'test_metrics': test_metrics,
        'confusion_matrix': confusion_matrix.tolist(),
        'classification_report': metrics_calculator.get_classification_report(),
        'misclassified_count': len(misclassified_samples),
        'total_test_samples': len(test_loader.dataset)
    }

    # 保存到JSON文件
    experiment_logger.save_metrics_json(test_results, 'test_results.json')

    logger.info("Test Results:")
    for metric, value in test_metrics.items():
        logger.info(f"  {metric}: {value:.4f}")
    logger.info(f"  Misclassified samples: {len(misclassified_samples)}/{len(test_loader.dataset)}")

    return test_metrics


def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()

    # 加载配置
    print(f"Loading configuration from {args.config}")
    config = load_config(args.config)

    # 覆盖配置
    override_config(config, args)

    # 设置可重现性
    repro_manager = ReproducibilityManager(
        experiment_name=config.experiment.name,
        save_dir=config.logging.log_dir
    )
    repro_manager.setup_reproducibility(
        seed=config.experiment.random_seed,
        deterministic=True,
        config=repro_manager.__dict__  # 简化的配置传递
    )

    # 设置设备
    device = setup_device(config)

    # 设置实验日志
    logger = ExperimentLogger(
        experiment_name=config.experiment.name,
        log_dir=config.logging.log_dir,
        use_tensorboard=config.logging.tensorboard
    )

    # 记录配置和环境信息
    logger.log_config(config.__dict__)

    try:
        # 创建模型
        model, model_info = create_model(config, device)
        logger.log_model_info(model_info)

        # 创建数据集和加载器
        train_loader, val_loader, test_loader = create_datasets_and_loaders(config)

        # 调试：检查配置加载
        print(f"DEBUG: config.validation.early_stopping_metric = {getattr(config.validation, 'early_stopping_metric', 'NOT_FOUND')}")

        # 创建训练器
        trainer_config = {
            'optimizer': config.training.optimizer,
            'scheduler': config.training.scheduler,
            'loss': config.training.loss,
            'mixed_precision': config.hardware.mixed_precision,
            'early_stopping_patience': config.validation.early_stopping_patience,
            'early_stopping_min_delta': config.validation.early_stopping_min_delta,
            'early_stopping_metric': getattr(config.validation, 'early_stopping_metric', 'recall'),
            'save_dir': config.logging.save_dir,
            'log_interval': config.logging.log_interval,
            'eval_interval': config.validation.eval_interval,
            'validation': config.validation  # 传递完整的validation配置
        }

        trainer = PulsarTrainer(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            config=trainer_config,
            device=device,
            logger=logger.logger
        )

        # 恢复检查点（如果指定）
        start_epoch = 0
        if args.resume:
            start_epoch, _ = load_checkpoint(
                model, trainer.optimizer, trainer.scheduler, args.resume, device
            )
            trainer.current_epoch = start_epoch

        # 开始训练
        logger.log_training_start(
            total_epochs=config.training.epochs,
            total_batches=len(train_loader)
        )

        training_history = trainer.train(config.training.epochs - start_epoch)

        # 记录训练完成
        best_metrics = {
            'best_recall': trainer.best_metric,  # 修正标签：实际存储的是召回率
            'best_epoch': trainer.best_epoch
        }
        logger.log_training_complete(
            total_time=sum([m.get('time', 0) for m in training_history['train_metrics']]),
            best_metrics=best_metrics
        )

        print(f"\nTraining completed!")
        print(f"Best recall: {trainer.best_metric:.4f} at epoch {trainer.best_epoch}")
        print(f"Model saved to: {trainer.save_dir}")

        # 评估测试集
        print(f"\nEvaluating on test set...")
        test_metrics = evaluate_test_set(
            model=model,
            test_loader=test_loader,
            device=device,
            logger=logger.logger,
            save_dir=trainer.save_dir,
            experiment_logger=logger
        )

        # 保存完整训练历史
        logger.save_training_history(training_history)

        # 保存最终结果摘要
        final_summary = {
            'best_validation_metrics': best_metrics,
            'test_metrics': test_metrics,
            'model_info': model_info,
            'training_epochs': trainer.best_epoch
        }
        logger.save_metrics_json(final_summary, 'experiment_summary.json')

        print(f"\nExperiment completed!")
        print(f"Results saved to: {logger.experiment_dir}")

    except Exception as e:
        logger.logger.error(f"Training failed: {str(e)}")
        raise

    finally:
        # 关闭日志记录器
        logger.close()


if __name__ == "__main__":
    main()
